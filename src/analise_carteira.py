#!/usr/bin/env python3
"""
Script para análise de carteira de ações
Lê dados de um arquivo CSV e gera análises de rendimento individual e do conjunto
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import yfinance as yf
from datetime import datetime, timedelta
import os

# Configuração de estilo
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def carregar_carteira(arquivo_csv):
    """Carrega dados da carteira do arquivo CSV"""
    try:
        carteira = pd.read_csv(arquivo_csv)
        carteira['data_compra'] = pd.to_datetime(carteira['data_compra'])
        return carteira
    except Exception as e:
        print(f"Erro ao carregar carteira: {e}")
        return None

def obter_dados_historicos(tickers, data_inicio):
    """Obtém dados históricos das ações"""
    print("Obtendo dados históricos...")
    dados = {}
    
    for ticker in tickers:
        try:
            stock = yf.Ticker(ticker)
            hist = stock.history(start=data_inicio, end=datetime.now())
            if not hist.empty:
                dados[ticker] = hist
                print(f"✓ Dados obtidos para {ticker}")
            else:
                print(f"✗ Nenhum dado encontrado para {ticker}")
        except Exception as e:
            print(f"✗ Erro ao obter dados para {ticker}: {e}")
    
    return dados

def calcular_rendimento_individual(carteira, dados_historicos):
    """Calcula rendimento individual de cada ação"""
    resultados = []
    
    for _, posicao in carteira.iterrows():
        ticker = posicao['ticker']
        quantidade = posicao['quantidade']
        data_compra = posicao['data_compra']
        preco_compra = posicao.get('preco_compra', None)
        
        if ticker in dados_historicos:
            hist = dados_historicos[ticker]
            
            # Se não temos preço de compra, usar o preço do dia da compra
            if pd.isna(preco_compra) or preco_compra == 0:
                try:
                    preco_compra = hist.loc[hist.index.date >= data_compra.date(), 'Close'].iloc[0]
                except:
                    preco_compra = hist['Close'].iloc[0]
            
            preco_atual = hist['Close'].iloc[-1]
            
            # Cálculos
            valor_investido = quantidade * preco_compra
            valor_atual = quantidade * preco_atual
            rendimento_absoluto = valor_atual - valor_investido
            rendimento_percentual = (rendimento_absoluto / valor_investido) * 100
            
            # Dados históricos desde a compra (incluindo o dia da compra)
            dados_desde_compra = hist[hist.index.date >= data_compra.date()].copy()
            if dados_desde_compra.empty:
                # Se não há dados desde a compra, usar os dados disponíveis
                dados_desde_compra = hist.copy()
            dados_desde_compra['Valor_Posicao'] = dados_desde_compra['Close'] * quantidade
            
            resultado = {
                'ticker': ticker,
                'quantidade': quantidade,
                'data_compra': data_compra,
                'preco_compra': preco_compra,
                'preco_atual': preco_atual,
                'valor_investido': valor_investido,
                'valor_atual': valor_atual,
                'rendimento_absoluto': rendimento_absoluto,
                'rendimento_percentual': rendimento_percentual,
                'dados_historicos': dados_desde_compra
            }
            
            resultados.append(resultado)
    
    return resultados

def calcular_rendimento_carteira(resultados):
    """Calcula rendimento total da carteira"""
    valor_total_investido = sum(r['valor_investido'] for r in resultados)
    valor_total_atual = sum(r['valor_atual'] for r in resultados)
    rendimento_total_absoluto = valor_total_atual - valor_total_investido
    rendimento_total_percentual = (rendimento_total_absoluto / valor_total_investido) * 100 if valor_total_investido > 0 else 0
    
    # Criar série temporal da carteira
    todas_datas = set()
    for resultado in resultados:
        todas_datas.update(resultado['dados_historicos'].index.date)
    
    todas_datas = sorted(todas_datas)
    valor_carteira_diario = []
    
    for data in todas_datas:
        valor_dia = 0
        for resultado in resultados:
            hist = resultado['dados_historicos']
            try:
                preco_dia = hist[hist.index.date <= data]['Close'].iloc[-1]
                valor_dia += preco_dia * resultado['quantidade']
            except:
                continue
        valor_carteira_diario.append(valor_dia)
    
    carteira_historico = pd.DataFrame({
        'Data': todas_datas,
        'Valor_Total': valor_carteira_diario
    })
    carteira_historico['Data'] = pd.to_datetime(carteira_historico['Data'])
    carteira_historico.set_index('Data', inplace=True)
    
    return {
        'valor_total_investido': valor_total_investido,
        'valor_total_atual': valor_total_atual,
        'rendimento_total_absoluto': rendimento_total_absoluto,
        'rendimento_total_percentual': rendimento_total_percentual,
        'historico': carteira_historico
    }

def gerar_graficos(resultados, carteira_total):
    """Gera gráficos de análise"""
    os.makedirs('results/figures', exist_ok=True)
    
    # 1. Gráfico individual de cada ação
    n_acoes = len(resultados)
    if n_acoes > 0:
        fig, axes = plt.subplots(n_acoes, 2, figsize=(15, 5*n_acoes))
        if n_acoes == 1:
            axes = axes.reshape(1, -1)
        
        for i, resultado in enumerate(resultados):
            ticker = resultado['ticker']
            hist = resultado['dados_historicos']
            
            # Gráfico de preço
            axes[i, 0].plot(hist.index, hist['Close'], label=f'{ticker} - Preço', linewidth=2)
            axes[i, 0].axhline(y=resultado['preco_compra'], color='red', linestyle='--', 
                              label=f'Preço Compra: R$ {resultado["preco_compra"]:.2f}')
            axes[i, 0].set_title(f'{ticker} - Evolução do Preço')
            axes[i, 0].set_ylabel('Preço (R$)')
            axes[i, 0].legend()
            axes[i, 0].grid(True, alpha=0.3)
            
            # Gráfico de valor da posição
            axes[i, 1].plot(hist.index, hist['Valor_Posicao'], label=f'{ticker} - Valor Posição', 
                           color='green', linewidth=2)
            axes[i, 1].axhline(y=resultado['valor_investido'], color='red', linestyle='--',
                              label=f'Valor Investido: R$ {resultado["valor_investido"]:.2f}')
            axes[i, 1].set_title(f'{ticker} - Valor da Posição')
            axes[i, 1].set_ylabel('Valor (R$)')
            axes[i, 1].legend()
            axes[i, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('results/figures/rendimento_individual.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # 2. Gráfico da carteira total
    if not carteira_total['historico'].empty:
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # Valor total da carteira
        ax1.plot(carteira_total['historico'].index, carteira_total['historico']['Valor_Total'], 
                label='Valor Total da Carteira', linewidth=2, color='blue')
        ax1.axhline(y=carteira_total['valor_total_investido'], color='red', linestyle='--',
                   label=f'Valor Investido: R$ {carteira_total["valor_total_investido"]:.2f}')
        ax1.set_title('Evolução do Valor Total da Carteira')
        ax1.set_ylabel('Valor (R$)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Rendimento percentual acumulado
        rendimento_pct = ((carteira_total['historico']['Valor_Total'] - carteira_total['valor_total_investido']) / 
                         carteira_total['valor_total_investido']) * 100
        ax2.plot(carteira_total['historico'].index, rendimento_pct, 
                label='Rendimento %', linewidth=2, color='green')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.set_title('Rendimento Percentual da Carteira')
        ax2.set_ylabel('Rendimento (%)')
        ax2.set_xlabel('Data')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('results/figures/carteira_total.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("Gráficos salvos em 'results/figures/':")
        print("  - rendimento_individual.png")
        print("  - carteira_total.png")
        print("Para visualizar os gráficos, abra os arquivos PNG na pasta results/figures/")

def gerar_relatorio(resultados, carteira_total):
    """Gera relatório resumo"""
    print("\n" + "="*60)
    print("RELATÓRIO DE ANÁLISE DA CARTEIRA")
    print("="*60)
    
    print(f"\nDATA DA ANÁLISE: {datetime.now().strftime('%d/%m/%Y %H:%M')}")
    
    print(f"\nRESUMO GERAL:")
    print(f"Valor Total Investido: R$ {carteira_total['valor_total_investido']:.2f}")
    print(f"Valor Atual da Carteira: R$ {carteira_total['valor_total_atual']:.2f}")
    print(f"Rendimento Absoluto: R$ {carteira_total['rendimento_total_absoluto']:.2f}")
    print(f"Rendimento Percentual: {carteira_total['rendimento_total_percentual']:.2f}%")
    
    print(f"\nDETALHE POR AÇÃO:")
    print("-" * 80)
    for resultado in resultados:
        print(f"\n{resultado['ticker']}:")
        print(f"  Quantidade: {resultado['quantidade']} ações")
        print(f"  Data da Compra: {resultado['data_compra'].strftime('%d/%m/%Y')}")
        print(f"  Preço de Compra: R$ {resultado['preco_compra']:.2f}")
        print(f"  Preço Atual: R$ {resultado['preco_atual']:.2f}")
        print(f"  Valor Investido: R$ {resultado['valor_investido']:.2f}")
        print(f"  Valor Atual: R$ {resultado['valor_atual']:.2f}")
        print(f"  Rendimento: R$ {resultado['rendimento_absoluto']:.2f} ({resultado['rendimento_percentual']:.2f}%)")

def main():
    """Função principal"""
    arquivo_carteira = 'carteira.csv'
    
    print("Iniciando análise da carteira...")
    
    # Carregar carteira
    carteira = carregar_carteira(arquivo_carteira)
    if carteira is None:
        return
    
    print(f"Carteira carregada: {len(carteira)} posições")
    
    # Obter dados históricos - começar a partir da data de início dos investimentos
    tickers = carteira['ticker'].unique()
    data_inicio = carteira['data_compra'].min()  # A partir da primeira compra
    
    dados_historicos = obter_dados_historicos(tickers, data_inicio)
    
    if not dados_historicos:
        print("Nenhum dado histórico foi obtido. Verifique os tickers.")
        return
    
    # Calcular rendimentos
    resultados = calcular_rendimento_individual(carteira, dados_historicos)
    carteira_total = calcular_rendimento_carteira(resultados)
    
    # Gerar gráficos
    gerar_graficos(resultados, carteira_total)
    
    # Gerar relatório
    gerar_relatorio(resultados, carteira_total)
    
    # Salvar resultados em CSV
    os.makedirs('results', exist_ok=True)
    df_resultados = pd.DataFrame([{
        'ticker': r['ticker'],
        'quantidade': r['quantidade'],
        'data_compra': r['data_compra'],
        'preco_compra': r['preco_compra'],
        'preco_atual': r['preco_atual'],
        'valor_investido': r['valor_investido'],
        'valor_atual': r['valor_atual'],
        'rendimento_absoluto': r['rendimento_absoluto'],
        'rendimento_percentual': r['rendimento_percentual']
    } for r in resultados])
    
    df_resultados.to_csv('results/analise_carteira.csv', index=False)
    print(f"\nResultados salvos em 'results/analise_carteira.csv'")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Script para mostrar resumo da carteira com gráfico simples
"""

import pandas as pd
import matplotlib.pyplot as plt
import yfinance as yf
from datetime import datetime, timed<PERSON><PERSON>

def mostrar_resumo():
    """Mostra resumo da carteira com gráfico simples"""
    
    # Ler dados da carteira
    try:
        carteira = pd.read_csv('carteira.csv')
        carteira['data_compra'] = pd.to_datetime(carteira['data_compra'])
    except:
        print("Erro ao ler carteira.csv")
        return
    
    print("="*50)
    print("RESUMO DA CARTEIRA")
    print("="*50)
    
    valor_total_investido = 0
    valor_total_atual = 0
    
    # Criar gráfico simples
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    for _, posicao in carteira.iterrows():
        ticker = posicao['ticker']
        quantidade = posicao['quantidade']
        preco_compra = posicao['preco_compra']
        data_compra = posicao['data_compra']
        
        # Obter preço atual
        try:
            stock = yf.Ticker(ticker)
            hist = stock.history(period='5d')
            preco_atual = hist['Close'].iloc[-1]
            
            # Obter dados desde a data de compra para gráfico
            hist_desde_compra = stock.history(start=data_compra.date())
            if hist_desde_compra.empty:
                # Se não há dados desde a compra, usar período de 1 mês
                hist_desde_compra = stock.history(period='1mo')
            
            valor_investido = quantidade * preco_compra
            valor_atual = quantidade * preco_atual
            rendimento = valor_atual - valor_investido
            rendimento_pct = (rendimento / valor_investido) * 100
            
            valor_total_investido += valor_investido
            valor_total_atual += valor_atual
            
            print(f"\n{ticker}:")
            print(f"  Quantidade: {quantidade} ações")
            print(f"  Preço Compra: R$ {preco_compra:.2f}")
            print(f"  Preço Atual: R$ {preco_atual:.2f}")
            print(f"  Valor Investido: R$ {valor_investido:.2f}")
            print(f"  Valor Atual: R$ {valor_atual:.2f}")
            print(f"  Rendimento: R$ {rendimento:.2f} ({rendimento_pct:.2f}%)")
            
            # Gráfico de preço
            ax1.plot(hist_desde_compra.index, hist_desde_compra['Close'], label=f'{ticker}', linewidth=2)
            ax1.axhline(y=preco_compra, linestyle='--', alpha=0.7, label=f'{ticker} Compra')

            # Gráfico de valor da posição
            valor_posicao = hist_desde_compra['Close'] * quantidade
            ax2.plot(hist_desde_compra.index, valor_posicao, label=f'{ticker} Posição', linewidth=2)
            ax2.axhline(y=valor_investido, linestyle='--', alpha=0.7, label=f'{ticker} Investido')
            
        except Exception as e:
            print(f"Erro ao processar {ticker}: {e}")
    
    # Resumo total
    rendimento_total = valor_total_atual - valor_total_investido
    rendimento_total_pct = (rendimento_total / valor_total_investido) * 100 if valor_total_investido > 0 else 0
    
    print(f"\n{'='*50}")
    print("TOTAL DA CARTEIRA:")
    print(f"Valor Investido: R$ {valor_total_investido:.2f}")
    print(f"Valor Atual: R$ {valor_total_atual:.2f}")
    print(f"Rendimento: R$ {rendimento_total:.2f} ({rendimento_total_pct:.2f}%)")
    print(f"{'='*50}")
    
    # Configurar gráficos
    ax1.set_title('Evolução dos Preços (desde início dos investimentos)')
    ax1.set_ylabel('Preço (R$)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    ax2.set_title('Valor das Posições (desde início dos investimentos)')
    ax2.set_ylabel('Valor (R$)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results/figures/resumo_carteira.png', dpi=300, bbox_inches='tight')
    plt.close()

    print(f"\nGráfico salvo em 'results/figures/resumo_carteira.png'")
    print("Para visualizar os gráficos, abra os arquivos PNG na pasta results/figures/")

if __name__ == "__main__":
    mostrar_resumo()
